import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';

/// 🌓 Enhanced Light Theme
final ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  scaffoldBackgroundColor: const Color(0xFFFAFAFA), // Soft light gray background
  cardColor: AppColors.white,
  primarySwatch: MaterialColor(0xFFfec53a, <int, Color>{
    50: Color(0xFFFFF8E1),
    100: Color(0xFFFFECB3),
    200: Color(0xFFFFE082),
    300: Color(0xFFFFD54F),
    400: Color(0xFFFFCA28),
    500: Color(0xFFfec53a),
    600: Color(0xFFFFB300),
    700: Color(0xFFFFA000),
    800: Color(0xFFFF8F00),
    900: Color(0xFFFF6F00),
  }),
  primaryColor: const Color(0xFFfec53a),
  colorScheme: const ColorScheme.light(
    primary: Color(0xFFfec53a),
    secondary: Color(0xFFfec53a),
    surface: AppColors.white,
    onPrimary: AppColors.black,
    onSecondary: AppColors.black,
    onSurface: AppColors.black,
  ),
  appBarTheme: const AppBarTheme(
    backgroundColor: AppColors.white,
    foregroundColor: AppColors.black,
    iconTheme: IconThemeData(color: AppColors.black),
    titleTextStyle: TextStyle(
      color: AppColors.black,
      fontSize: 20,
      fontWeight: FontWeight.bold,
      fontFamily: 'HacenTunisia',
    ),
    elevation: 0,
    surfaceTintColor: Colors.transparent,
    shadowColor: Colors.black12,
  ),
  textTheme: const TextTheme(
    displayLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.bold,
      fontSize: 32,
    ),
    displayMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.w600,
      fontSize: 28,
    ),
    headlineLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.bold,
      fontSize: 24,
    ),
    headlineMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.w600,
      fontSize: 20,
    ),
    titleLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.w600,
      fontSize: 18,
    ),
    titleMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.w500,
      fontSize: 16,
    ),
    bodyLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontSize: 16,
    ),
    bodyMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontSize: 14,
    ),
    bodySmall: TextStyle(
      fontFamily: 'HacenTunisia',
      color: Color(0xFF666666),
      fontSize: 12,
    ),
    labelLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.w500,
      fontSize: 14,
    ),
  ),
  iconTheme: const IconThemeData(color: AppColors.black),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFFfec53a),
      foregroundColor: AppColors.black,
      textStyle: const TextStyle(
        fontWeight: FontWeight.bold,
        fontFamily: 'HacenTunisia',
      ),
      elevation: 2,
      shadowColor: const Color(0xFFfec53a).withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: const Color(0xFFfec53a),
      textStyle: const TextStyle(
        fontWeight: FontWeight.w600,
        fontFamily: 'HacenTunisia',
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: AppColors.white,
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.grey.shade300),
      borderRadius: BorderRadius.circular(16),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.grey.shade300),
      borderRadius: BorderRadius.circular(16),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Color(0xFFfec53a), width: 2),
      borderRadius: BorderRadius.circular(16),
    ),
    errorBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Colors.red, width: 1),
      borderRadius: BorderRadius.circular(16),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Colors.red, width: 2),
      borderRadius: BorderRadius.circular(16),
    ),
    labelStyle: const TextStyle(
      color: Color(0xFFfec53a),
      fontFamily: 'HacenTunisia',
    ),
    hintStyle: TextStyle(
      color: Colors.grey.shade600,
      fontFamily: 'HacenTunisia',
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
  ),
  sliderTheme: SliderThemeData(
    activeTrackColor: AppColors.yellow,
    inactiveTrackColor: Colors.grey[300],
    thumbColor: AppColors.yellow,
    overlayColor: AppColors.yellow.withValues(alpha: 0.3),
  ),

);

/// 🌑 Dark Theme
final ThemeData darkTheme = ThemeData(
  brightness: Brightness.dark,
  scaffoldBackgroundColor: AppColors.black,
  cardColor: AppColors.darkGrey,
  appBarTheme: const AppBarTheme(
    color: AppColors.yellow,
    iconTheme: IconThemeData(color: AppColors.black),
    titleTextStyle: TextStyle(
      color: AppColors.black,
      fontSize: 20,
      fontWeight: FontWeight.bold,
    ),
  ),
  textTheme: const TextTheme(
    displayLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
      fontWeight: FontWeight.bold,
    ),
    displayMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
    bodyLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
    bodyMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
    labelLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
  ),
  iconTheme: const IconThemeData(color: AppColors.white),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.yellow,
      foregroundColor: AppColors.black,
      textStyle: const TextStyle(fontWeight: FontWeight.bold),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.white,
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: AppColors.darkGrey,
    border: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.yellow.withValues(alpha: 0.8)),
      borderRadius: BorderRadius.circular(12),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.yellow.withValues(alpha: 0.6)),
      borderRadius: BorderRadius.circular(12),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: AppColors.yellow, width: 2),
      borderRadius: BorderRadius.circular(12),
    ),
    labelStyle: const TextStyle(color: AppColors.yellow),
    hintStyle: const TextStyle(color: Colors.grey),
  ),
  sliderTheme: SliderThemeData(
    activeTrackColor: AppColors.yellow,
    inactiveTrackColor: Colors.grey[600],
    thumbColor: AppColors.yellow,
    overlayColor: AppColors.yellow.withValues(alpha: 0.3),
  ),
  colorScheme: const ColorScheme.dark().copyWith(
    primary: AppColors.yellow,
    secondary: AppColors.yellow,
    surface: AppColors.darkGrey,
    onPrimary: AppColors.black,
    onSecondary: AppColors.black,
  ),
);
