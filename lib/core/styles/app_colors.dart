import 'package:flutter/material.dart';

abstract class AppColors {
  static const Color black = Color(0xff000000);
  static const Color yellow = Color(0xffFEC53A);
  static const Color white = Color(0xFFFFFFFF);
  static const Color darkGrey = Color(0xFF303030);
  static const Color lightGrey = Color(0xFFB0B0B0);
  static const Color green = Color(0xff00806E);
  static const Color brown = Color(0xffA05135);
  static const Color lightWhite = Color(0xffFFFCF9);
  static const Color successGreen = Color(0xFF00ADA8);
  static const Color errorRed = Color(0xFFD72F5A);
  static const Color lightBrown2 = Color(0xffA05235);
  static const Color lightWhite2 = Color(0xffFBFBFB);
  static const Color darkBlue = Color(0xff1F2A37);
  static const Color darkGrey2 = Color(0xff101828);
  static const Color darkGrey3 = Color(0xff667085);
  static const Color red = Color(0xffFF4B55);
  static const Color lightGrey2 = Color(0xffDFE4EA);
  static const Color red2 = Color(0xffF23030);
  static const Color darkGrey4 = Color(0xff637381);
  static const Color darkGrey5 = Color(0xff8899A8);
  static const Color darkGrey6 = Color(0xff353535);
  static const Color lightGrey3 = Color(0xffB7B7B7);
  static const Color lightGrey4 = Color(0xffC6C6C6);
  static const Color darkGrey7 = Color(0xff2B2B2B);
  static const Color lightGrey5 = Color(0xff9B9B9B);
  static const Color lightGrey6 = Color(0xffF9F9F9);
  static const Color lightGrey7 = Color(0xffBFBFBF);
  static const Color darkGrey8 = Color(0xff575757);
  static const Color lightGrey8 = Color(0xffE0E0E0);
  static const Color lightYellow = Color(0xffFEC53A);
  static const Color lightGrey9 = Color(0xff9DB2CE);
  static const Color lightGrey10 = Color(0xff3b3b3b);
  static const Color darkGrey9 = Color(0xff656565);

  // Additional colors for FCM notifications
  static const Color primary = Color(0xffFEC53A);
  static const Color grey = Color(0xFF9E9E9E);
}
